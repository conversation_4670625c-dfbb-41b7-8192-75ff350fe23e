'use client';

import { useAuthStore } from '@/store';
import { useEffect } from 'react';
import { supabase } from '@/lib/db';

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const { setUser, setUserRole } = useAuthStore();

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session?.user) {
        // Fetch user role from user_roles table
        supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', session.user.id)
          .single()
          .then(({ data: roleData }) => {
            setUser(session.user);
            setUserRole(roleData?.role ?? null);
          });
      } else {
        setUser(null);
        setUserRole(null);
      }
    });

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session?.user) {
        // Fetch user role from user_roles table
        const { data: roleData } = await supabase
          .from('user_roles')
          .select('role')
          .eq('user_id', session.user.id)
          .single();

        setUser(session.user);
        setUserRole(roleData?.role ?? null);
      } else if (event === 'SIGNED_OUT') {
        setUser(null);
        setUserRole(null);
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [setUser, setUserRole]);

  return <>{children}</>;
}
