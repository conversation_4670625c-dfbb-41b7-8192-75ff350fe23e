import { NextRequest, NextResponse } from 'next/server';
import { supabase } from '@/lib/db';

// GET - Fetch workshop configuration
export async function GET() {
  try {
    const { data, error } = await supabase
      .from('workshop_config')
      .select('*')
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
      throw error;
    }

    return NextResponse.json(data || null);
  } catch (error) {
    console.error('Error fetching workshop config:', error);
    return NextResponse.json(
      { error: 'Failed to fetch workshop configuration' },
      { status: 500 }
    );
  }
}

// POST - Create workshop configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const { data, error } = await supabase
      .from('workshop_config')
      .insert([body])
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error creating workshop config:', error);
    return NextResponse.json(
      { error: 'Failed to create workshop configuration' },
      { status: 500 }
    );
  }
}

// PUT - Update workshop configuration
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { config_id, ...updateData } = body;
    
    const { data, error } = await supabase
      .from('workshop_config')
      .update(updateData)
      .eq('config_id', config_id)
      .select()
      .single();

    if (error) throw error;

    return NextResponse.json(data);
  } catch (error) {
    console.error('Error updating workshop config:', error);
    return NextResponse.json(
      { error: 'Failed to update workshop configuration' },
      { status: 500 }
    );
  }
}

// DELETE - Delete workshop configuration
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const config_id = searchParams.get('config_id');

    if (!config_id) {
      return NextResponse.json(
        { error: 'config_id is required' },
        { status: 400 }
      );
    }

    const { error } = await supabase
      .from('workshop_config')
      .delete()
      .eq('config_id', config_id);

    if (error) throw error;

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting workshop config:', error);
    return NextResponse.json(
      { error: 'Failed to delete workshop configuration' },
      { status: 500 }
    );
  }
}
